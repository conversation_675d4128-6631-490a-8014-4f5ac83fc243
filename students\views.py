from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from .models import Student, Parent, Class, Grade
# Temporarily comment out forms import to test server
# from .forms import StudentForm, ParentForm, ClassForm, GradeForm

# Create simple placeholder forms
from django import forms

class StudentForm(forms.ModelForm):
    class Meta:
        model = Student
        fields = ['student_id', 'first_name', 'last_name', 'date_of_birth', 'gender']

class ParentForm(forms.ModelForm):
    class Meta:
        model = Parent
        fields = ['father_name', 'father_phone', 'home_address']

class ClassForm(forms.ModelForm):
    class Meta:
        model = Class
        fields = ['name', 'grade', 'academic_year']

class GradeForm(forms.ModelForm):
    class Meta:
        model = Grade
        fields = ['name', 'level']
from django.utils.translation import gettext_lazy as _

# Student Management Views
class StudentDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'students/student_dashboard.html'

class StudentListView(LoginRequiredMixin, ListView):
    model = Student
    template_name = 'students/student_list.html'
    context_object_name = 'students'
    paginate_by = 20

    def get_queryset(self):
        queryset = Student.objects.select_related('user', 'parent', 'current_class').all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(student_id__icontains=search) |
                Q(admission_number__icontains=search)
            )
        return queryset

class StudentDetailView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'students/student_detail.html'
    context_object_name = 'student'

class StudentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.add_student'
    success_url = reverse_lazy('students:list')

    def form_valid(self, form):
        messages.success(self.request, _('Student added successfully!'))
        return super().form_valid(form)

class StudentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Student
    form_class = StudentForm
    template_name = 'students/student_form.html'
    permission_required = 'students.change_student'

    def get_success_url(self):
        return reverse_lazy('students:detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, _('Student updated successfully!'))
        return super().form_valid(form)

class StudentDeleteView(LoginRequiredMixin, PermissionRequiredMixin, DeleteView):
    model = Student
    template_name = 'students/student_confirm_delete.html'
    permission_required = 'students.delete_student'
    success_url = reverse_lazy('students:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Student deleted successfully!'))
        return super().delete(request, *args, **kwargs)

class StudentAdvancedSearchView(LoginRequiredMixin, TemplateView):
    template_name = 'students/advanced_search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grades'] = Grade.objects.all()
        context['classes'] = Class.objects.all()
        return context

class DistributeStudentsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/distribute_students.html'
    permission_required = 'students.change_student'

class StudentTransferView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/student_transfer.html'
    permission_required = 'students.change_student'

class StudentCardsView(LoginRequiredMixin, TemplateView):
    template_name = 'students/student_cards.html'

# Parent Management Views
class ParentListView(LoginRequiredMixin, ListView):
    model = Parent
    template_name = 'students/parent_list.html'
    context_object_name = 'parents'
    paginate_by = 20

class ParentDetailView(LoginRequiredMixin, DetailView):
    model = Parent
    template_name = 'students/parent_detail.html'
    context_object_name = 'parent'

class ParentCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Parent
    form_class = ParentForm
    template_name = 'students/parent_form.html'
    permission_required = 'students.add_parent'
    success_url = reverse_lazy('students:parent_list')

class ParentUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Parent
    form_class = ParentForm
    template_name = 'students/parent_form.html'
    permission_required = 'students.change_parent'

    def get_success_url(self):
        return reverse_lazy('students:parent_detail', kwargs={'pk': self.object.pk})

class AddParentManualView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/add_parent_manual.html'
    permission_required = 'students.add_parent'

# Class Management Views
class ClassListView(LoginRequiredMixin, ListView):
    model = Class
    template_name = 'students/class_list.html'
    context_object_name = 'classes'

class ClassDetailView(LoginRequiredMixin, DetailView):
    model = Class
    template_name = 'students/class_detail.html'
    context_object_name = 'class_obj'

class ClassCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Class
    form_class = ClassForm
    template_name = 'students/class_form.html'
    permission_required = 'students.add_class'
    success_url = reverse_lazy('students:class_list')

class ClassUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Class
    form_class = ClassForm
    template_name = 'students/class_form.html'
    permission_required = 'students.change_class'

    def get_success_url(self):
        return reverse_lazy('students:class_detail', kwargs={'pk': self.object.pk})

# Grade Management Views
class GradeListView(LoginRequiredMixin, ListView):
    model = Grade
    template_name = 'students/grade_list.html'
    context_object_name = 'grades'

class GradeCreateView(LoginRequiredMixin, PermissionRequiredMixin, CreateView):
    model = Grade
    form_class = GradeForm
    template_name = 'students/grade_form.html'
    permission_required = 'students.add_grade'
    success_url = reverse_lazy('students:grade_list')

class GradeUpdateView(LoginRequiredMixin, PermissionRequiredMixin, UpdateView):
    model = Grade
    form_class = GradeForm
    template_name = 'students/grade_form.html'
    permission_required = 'students.change_grade'
    success_url = reverse_lazy('students:grade_list')

# Student Affairs Views
class StudentInfractionsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/infractions.html'
    permission_required = 'students.change_student'

class SuspensionAndBlockView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/suspension_block.html'
    permission_required = 'students.change_student'

class VacationRequestsView(LoginRequiredMixin, TemplateView):
    template_name = 'students/vacation_requests.html'

class ApproveVacationRequestsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/approve_vacation.html'
    permission_required = 'students.change_student'

class StudentSecondLanguageView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/second_language.html'
    permission_required = 'students.change_student'

# Documents and Attachments Views
class StudentDocumentsView(LoginRequiredMixin, TemplateView):
    template_name = 'students/documents.html'

class StudentDocumentReceiverView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/document_receiver.html'
    permission_required = 'students.change_student'

class StudentAttachmentsView(LoginRequiredMixin, TemplateView):
    template_name = 'students/attachments.html'

# Electronic Registration
class ElectronicRegistrationView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/electronic_registration.html'
    permission_required = 'students.add_student'

# Settings Views
class StudentSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/settings.html'
    permission_required = 'students.change_student'

class StudentFieldsSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/fields_settings.html'
    permission_required = 'students.change_student'

class StudentTabsSettingsView(LoginRequiredMixin, PermissionRequiredMixin, TemplateView):
    template_name = 'students/tabs_settings.html'
    permission_required = 'students.change_student'
